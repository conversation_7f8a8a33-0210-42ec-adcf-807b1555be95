import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/dropdown_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class AIGeneratedObjectScreen extends StatefulWidget {
  const AIGeneratedObjectScreen({super.key});

  @override
  State<AIGeneratedObjectScreen> createState() => _AIGeneratedObjectScreenState();
}

class _AIGeneratedObjectScreenState extends State<AIGeneratedObjectScreen> {
  late TextEditingController chatController;
  late MultimediaService _multimediaService;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    chatController = TextEditingController();
    _multimediaService = MultimediaService();
  }

  @override
  void dispose() {
    chatController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (chatController.text.trim().isNotEmpty) {
      // Handle message sending logic here
      Logger.info('Sending message: ${chatController.text}');
      // You can add your API call logic here
      chatController.clear();
    }
  }

  void _cancelRequest() {
    // Handle cancel request logic here
    setState(() {
      isLoading = false;
    });
  }

  void _handleFileSelection(String fileName, String filePath) {
    // Handle file selection logic here
    Logger.info('File selected: $fileName at $filePath');
  }

  void _toggleRecording() {
    // Handle recording toggle logic here
    Logger.info('Toggle recording');
  }

  

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //backgroundColor: Color(0xfff6f6f6),
      body: Column(
        children: [
          // Top navigation bar
          _buildTopNavigation(context),

          // Main content area
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                 color: Color(0xffF7F9FB),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 94),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // Spacer to push content down
                  const Spacer(),

                  // Text above chat field
                  Padding(
                    padding: const EdgeInsets.only(bottom: 40),
                    child: Text(
                      'I will help you to create your entity. Please tell me what entity you want to create?',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: "TiemposText",
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),

                  // Chat field at the bottom
                  ChatField(
                    isGeneralLoading: isLoading,
                    isFileLoading: false,
                    isSpeechLoading: false,
                    onSendMessage: _sendMessage,
                    onCancelRequest: _cancelRequest,
                    onFileSelected: _handleFileSelection,
                    onToggleRecording: _toggleRecording,
                    controller: chatController,
                    multimediaService: _multimediaService,
                    height: 56,
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopNavigation(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 94, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () {
              Provider.of<WebHomeProvider>(context, listen: false)
                  .currentScreenIndex = ScreenConstants.webMyObject;
            },
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Row(
                children: [
                 SvgPicture.asset(
                  'assets/images/arrow-left.svg',
                 
                 ),
                  const SizedBox(width: 8),
                  Text(
                    'Previous page',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xff5D5D5D),
                      fontFamily: "TiemposText",
                    ),
                  ),
                ],
              ),
            ),
          ),

          const Spacer(),

          // Center navigation items
          Row(
            children: [
              _buildNavItem(
               iconPath: 'assets/images/agent.svg',
                //iconData: Icons.smart_toy,
                label: '3 Agent(V001)',
                isActive: false,
              ),
              const SizedBox(width: 16),
              _buildNavItem(
              iconPath:  'assets/images/cube-box.svg',
              //  iconData: Icons.inventory_2,
                label: '12 Objects(V001)',
                isActive: false,
              ),
              const SizedBox(width: 16),
              _buildNavItem(
                iconPath: 'assets/images/square-box-uncheck.svg',
                //iconData: Icons.lightbulb_outline,
                label: '15 Solutions',
                isActive: false,
              ),
            ],
          ),

          const Spacer(),

          // Right side - User info dropdown
          _UserInfoDropdown(),
        ],
      ),
    );
  }

  Widget _buildNavItem({
   
    required String iconPath,
    required String label,
    required bool isActive,
  }) {
    return Container(
      //padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isActive ? Colors.blue.shade50 : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        border: isActive
            ? Border.all(color: Colors.blue.shade200, width: 1)
            : null,
      ),
      child: Row(
        children: [
           SvgPicture.asset(
              iconPath,
              width: AppSpacing.size14,
              height: AppSpacing.size14,
              colorFilter: ColorFilter.mode(
                Colors.black,
                BlendMode.srcIn,
              ),
            ),
          // Icon(
          //   iconData,
          //   size: 10,
          //   color: isActive ? Colors.blue.shade600 : Colors.black,
          // ),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              color: isActive ? Colors.blue.shade600 : Colors.black,
              fontFamily: "TiemposText",
            ),
          ),
        ],
      ),
    );
  }
}

class _UserInfoDropdown extends StatefulWidget {
  @override
  State<_UserInfoDropdown> createState() => _UserInfoDropdownState();
}

class _UserInfoDropdownState extends State<_UserInfoDropdown> {
  bool isDropdownOpen = false;

  @override
  Widget build(BuildContext context) {
    final dropdownState = Provider.of<DropdownState>(context);
    return GestureDetector(
      onTap: () {
        if (dropdownState.isOpen) {
          dropdownState.close();
        } 
      },
      child: Stack(
        children: [
          // Main dropdown trigger
          GestureDetector(
            onTap: () {
              dropdownState.toggle();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(
                  color: isDropdownOpen ? Color(0xff0058FF) : Colors.grey.shade300,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.keyboard_arrow_down,
                    size:16,
                      color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 2),
                  Text(
                    'BET',
                    style: TextStyle(
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                      color:Color(0xff5D5D5D),
                      fontFamily: "TiemposText",
                    ),
                  ),
                  const SizedBox(width: 24),
                  Text(
                    '1297',
                    style: TextStyle(
                      fontSize: 9,
                      color: Color(0xff5D5D5D),
                      fontFamily: "TiemposText",
                    ),
                  ),
                   const SizedBox(width: 2),
                ],
              ),
            ),
          ),

          // Dropdown content
         if (dropdownState.isOpen)
            Positioned(
              top: 32,
              right: 0,
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(4),
                child: Container(
                  width: 120,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Color(0xff0058FF), width: 1),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Agent:',
                            style: TextStyle(
                              fontSize: 12,
                              fontFamily: "TiemposText",
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            '12',
                            style: TextStyle(
                              fontSize: 12,
                              fontFamily: "TiemposText",
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Attribute:',
                            style: TextStyle(
                              fontSize: 12,
                              fontFamily: "TiemposText",
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            '765',
                            style: TextStyle(
                              fontSize: 12,
                              fontFamily: "TiemposText",
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
