import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class AIGeneratedObjectScreen extends StatefulWidget {
  const AIGeneratedObjectScreen({super.key});

  @override
  State<AIGeneratedObjectScreen> createState() => _AIGeneratedObjectScreenState();
}

class _AIGeneratedObjectScreenState extends State<AIGeneratedObjectScreen> {
  late TextEditingController chatController;
  late MultimediaService _multimediaService;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    chatController = TextEditingController();
    _multimediaService = MultimediaService();
  }

  @override
  void dispose() {
    chatController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (chatController.text.trim().isNotEmpty) {
      // Handle message sending logic here
      Logger.info('Sending message: ${chatController.text}');
      // You can add your API call logic here
      chatController.clear();
    }
  }

  void _cancelRequest() {
    // Handle cancel request logic here
    setState(() {
      isLoading = false;
    });
  }

  void _handleFileSelection(String fileName, String filePath) {
    // Handle file selection logic here
    Logger.info('File selected: $fileName at $filePath');
  }

  void _toggleRecording() {
    // Handle recording toggle logic here
    Logger.info('Toggle recording');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff6f6f6),
      body: Column(
        children: [
          // Top navigation bar
          _buildTopNavigation(context),

          // Main content area
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 94),
              child: Column(
                children: [
                  const SizedBox(height: 40),

                  // Main content - centered message
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'I will help you to create your entity. Please tell me what entity you want to create?',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w400,
                              fontFamily: "TiemposText",
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ),

                  // Chat field at the bottom
                  ChatField(
                    isGeneralLoading: isLoading,
                    isFileLoading: false,
                    isSpeechLoading: false,
                    onSendMessage: _sendMessage,
                    onCancelRequest: _cancelRequest,
                    onFileSelected: _handleFileSelection,
                    onToggleRecording: _toggleRecording,
                    controller: chatController,
                    multimediaService: _multimediaService,
                    height: 56,
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopNavigation(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 94, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () {
              Provider.of<WebHomeProvider>(context, listen: false)
                  .currentScreenIndex = ScreenConstants.webMyObject;
            },
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Row(
                children: [
                  Icon(
                    Icons.arrow_back,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Previous page',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ],
              ),
            ),
          ),

          const Spacer(),

          // Center navigation items
          Row(
            children: [
              _buildNavItem(
                iconData: Icons.smart_toy,
                label: '3 Agent (V001)',
                isActive: true,
              ),
              const SizedBox(width: 32),
              _buildNavItem(
                iconData: Icons.inventory_2,
                label: '12 Objects (V001)',
                isActive: false,
              ),
              const SizedBox(width: 32),
              _buildNavItem(
                iconData: Icons.lightbulb_outline,
                label: '15 Solutions',
                isActive: false,
              ),
            ],
          ),

          const Spacer(),

          // Right side - User info
          Row(
            children: [
              Text(
                'BET',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  fontFamily: "TiemposText",
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'V007',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontFamily: "TiemposText",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData iconData,
    required String label,
    required bool isActive,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isActive ? Colors.blue.shade50 : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        border: isActive
            ? Border.all(color: Colors.blue.shade200, width: 1)
            : null,
      ),
      child: Row(
        children: [
          Icon(
            iconData,
            size: 16,
            color: isActive ? Colors.blue.shade600 : Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              color: isActive ? Colors.blue.shade600 : Colors.grey.shade600,
              fontFamily: "TiemposText",
            ),
          ),
        ],
      ),
    );
  }
}
