import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../../providers/web_home_provider.dart';

class SolutionDropdown extends StatefulWidget {
  final VoidCallback? onAIGeneratedSelected;
  final VoidCallback? onManualCreationSelected;

  const SolutionDropdown({
    super.key,
    this.onAIGeneratedSelected,
    this.onManualCreationSelected,
  });

  @override
  State<SolutionDropdown> createState() => _SolutionDropdownState();
}

class _SolutionDropdownState extends State<SolutionDropdown> {
  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<WebHomeProvider>(context);
    
    // Only show dropdown if Solution is selected
    if (provider.selectedQuickMessage != 'Solution') {
      return SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(top: 8),
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 200,
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Color(0xffD0D0D0), width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI Generated option
              _buildDropdownOption(
                icon: Icons.auto_awesome,
                text: 'AI Generated',
                isSelected: provider.selectedSolutionOption == 'AI Generated',
                onTap: () {
                  provider.selectedSolutionOption = 'AI Generated';
                  if (widget.onAIGeneratedSelected != null) {
                    widget.onAIGeneratedSelected!();
                  }
                },
              ),
              SizedBox(height: 4),
              // Manual Creation option
              _buildDropdownOption(
                icon: Icons.edit,
                text: 'Manual Creation',
                isSelected: provider.selectedSolutionOption == 'Manual Creation',
                onTap: () {
                  provider.selectedSolutionOption = 'Manual Creation';
                  if (widget.onManualCreationSelected != null) {
                    widget.onManualCreationSelected!();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownOption({
    required IconData icon,
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xffF0F8FF) : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
          border: isSelected 
            ? Border.all(color: Color(0xff0058FF), width: 1)
            : null,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Color(0xff0058FF) : Colors.grey.shade600,
            ),
            SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: isSelected ? Color(0xff0058FF) : Colors.black,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
