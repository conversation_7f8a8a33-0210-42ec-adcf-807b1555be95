import 'package:flutter/material.dart';

class CreateObjectPopup extends StatelessWidget {
  final VoidCallback onAIGeneratedTap;
  final VoidCallback onManualCreationTap;

  const CreateObjectPopup({
    super.key,
    required this.onAIGeneratedTap,
    required this.onManualCreationTap,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 200,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // AI Generated option
            _PopupOption(
              icon: Icon(
                Icons.auto_awesome,
                size: 16,
                color: Colors.black,
              ),
              text: 'AI Generated',
              onTap: onAIGeneratedTap,
            ),

            // Divider
            Container(
              height: 1,
              color: Colors.grey.shade200,
              margin: const EdgeInsets.symmetric(horizontal: 12),
            ),

            // Manual Creation option
            _PopupOption(
              icon: Icon(
                Icons.edit,
                size: 16,
                color: Colors.black,
              ),
              text: 'Manual Creation',
              onTap: onManualCreationTap,
            ),
          ],
        ),
      ),
    );
  }
}

class _PopupOption extends StatefulWidget {
  final Widget icon;
  final String text;
  final VoidCallback onTap;

  const _PopupOption({
    required this.icon,
    required this.text,
    required this.onTap,
  });

  @override
  State<_PopupOption> createState() => _PopupOptionState();
}

class _PopupOptionState extends State<_PopupOption> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isHovered ? const Color(0xFFFFC1CB) : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              widget.icon,
              const SizedBox(width: 8),
              Text(
                widget.text,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
