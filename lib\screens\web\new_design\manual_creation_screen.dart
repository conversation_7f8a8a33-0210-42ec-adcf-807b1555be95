import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/web_home_provider.dart';
import '../../../l10n/app_localizations.dart';
import '../../../theme/spacing.dart';
import 'widgets/chat_widgets/chat_field.dart';
import 'widgets/sidebar.dart';

class ManualCreationScreen extends StatefulWidget {
  @override
  State<ManualCreationScreen> createState() => _ManualCreationScreenState();
}

class _ManualCreationScreenState extends State<ManualCreationScreen> {
  late TextEditingController chatController;

  @override
  void initState() {
    super.initState();
    chatController = TextEditingController();
  }

  @override
  void dispose() {
    chatController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final text = chatController.text.trim();
    if (text.isNotEmpty) {
      // Handle message sending logic here
      print('Sending message: $text');
      chatController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          // Sidebar
          Sidebar(),

          // Main content area
          Expanded(
            child: Column(
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(AppSpacing.md),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Back button
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(Icons.arrow_back, color: Colors.grey.shade600),
                      ),
                      SizedBox(width: AppSpacing.xs),
                      Text(
                        'Manual Creation',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                        ),
                      ),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(AppSpacing.lg),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Welcome message
                        Text(
                          'Hi testuser12, How can I help you?',
                          style: TextStyle(
                            fontSize: 34,
                            fontFamily: 'TiemposText',
                          ),
                          textAlign: TextAlign.center,
                        ),

                        SizedBox(height: AppSpacing.xl),

                        // Chat field
                        Container(
                          constraints: BoxConstraints(maxWidth: 600),
                          child: ChatField(
                            controller: chatController,
                            isGeneralLoading: false,
                            isFileLoading: false,
                            isSpeechLoading: false,
                            onSendMessage: _sendMessage,
                            onFileSelected: (fileName, filePath) {
                              // Handle file selection
                              print('File selected: $fileName at $filePath');
                            },
                            onToggleRecording: () {
                              // Handle recording toggle
                              print('Recording toggled');
                            },
                            parentState: this,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
