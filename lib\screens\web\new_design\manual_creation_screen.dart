import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'widgets/sidebar.dart';

class ManualCreationScreen extends StatefulWidget {
  const ManualCreationScreen({super.key});

  @override
  State<ManualCreationScreen> createState() => _ManualCreationScreenState();
}

class _ManualCreationScreenState extends State<ManualCreationScreen> {
  late TextEditingController textController;
  String? hoveredIcon;

  @override
  void initState() {
    super.initState();
    textController = TextEditingController();
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          // Sidebar
          Sidebar(),

          // Main content area
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with back button
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_back, color: Colors.grey),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Previous page',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                          fontFamily: 'TiemposText',
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 24),

                  // Title
                  Text(
                    'Create Your Agents',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),

                  SizedBox(height: 32),

                  // Main content area with icons and text field
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Left side - Icons column
                        Container(
                          width: 60,
                          child: Column(
                            children: [
                              SizedBox(height: 20), // Align with text field

                              // Agents icon
                              _buildIconWithTooltip(
                                iconPath: 'assets/images/agents_icon.svg',
                                tooltipText: 'Agents',
                                onTap: () {
                                  // Handle agents tap
                                },
                              ),

                              SizedBox(height: 16),

                              // DataSets icon
                              _buildIconWithTooltip(
                                iconPath: 'assets/images/datasets_icon.svg',
                                tooltipText: 'DataSets',
                                onTap: () {
                                  // Handle datasets tap
                                },
                              ),

                              SizedBox(height: 16),

                              // Workflows icon
                              _buildIconWithTooltip(
                                iconPath: 'assets/images/workflows_icon.svg',
                                tooltipText: 'Workflows',
                                onTap: () {
                                  // Handle workflows tap
                                },
                              ),
                            ],
                          ),
                        ),

                        SizedBox(width: 16),

                        // Right side - Large text field
                        Expanded(
                          child: Stack(
                            children: [
                              // Main text field
                              Container(
                                height: 400,
                                decoration: BoxDecoration(
                                  border: Border.all(color: Color(0xffD0D0D0), width: 1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: TextField(
                                  controller: textController,
                                  maxLines: null,
                                  expands: true,
                                  textAlignVertical: TextAlignVertical.top,
                                  decoration: InputDecoration(
                                    hintText: 'Type your message here...',
                                    hintStyle: TextStyle(
                                      color: Colors.grey.shade500,
                                      fontSize: 16,
                                      fontFamily: 'TiemposText',
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.all(16),
                                  ),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontFamily: 'TiemposText',
                                    color: Colors.black,
                                  ),
                                ),
                              ),

                              // Tooltip overlay
                              if (hoveredIcon != null)
                                Positioned(
                                  top: 16,
                                  left: 16,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.black,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      hoveredIcon!,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontFamily: 'TiemposText',
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 24),

                  // Bottom buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Cancel button
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          side: BorderSide(color: Color(0xffD0D0D0), width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                      ),

                      SizedBox(width: 12),

                      // Validate button
                      ElevatedButton(
                        onPressed: () {
                          // Handle validate action
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xff0058FF),
                          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        child: Text(
                          'Validate',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIconWithTooltip({
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        setState(() {
          hoveredIcon = tooltipText;
        });
      },
      onExit: (_) {
        setState(() {
          hoveredIcon = null;
        });
      },
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Color(0xffD0D0D0), width: 1),
          ),
          child: Center(
            child: SvgPicture.asset(
              iconPath,
              width: 20,
              height: 20,
              colorFilter: ColorFilter.mode(
                Colors.grey.shade600,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
