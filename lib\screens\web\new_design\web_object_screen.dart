import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/screens/web/new_design/widgets/create_object_popup.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class Book {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String versionNumber;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  Book({
    required this.title,
    this.subtitle = '',
    required this.imageUrl,
    required this.versionNumber,
    this.isDraft = false,
    this.imageWidth = 136.0,
    this.imageHeight = 200.0,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String? ?? '',
      imageUrl: json['imageUrl'] as String,
      versionNumber: json['versionNumber'] as String? ?? '',
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 136.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 200.0,
    );
  }
}

class WebObjectScreen extends StatefulWidget {
  const WebObjectScreen({super.key});

  @override
  State<WebObjectScreen> createState() => _WebObjectScreenState();
}

class _WebObjectScreenState extends State<WebObjectScreen> {
  late List<Book> books;
  bool isLoading = true;

  // JSON string containing book data
  static const String booksJsonString = '''
{
   "books": [
    {
      "title": "Customer Customer Customer",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Product Product Product",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Address Address Address",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals",
      "versionNumber": "V00172",
       "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Courier & Logistics Courier & Logistics",
      "versionNumber": "V00172",
       "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fitness & Wellness Fitness & Wellness",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  void _loadBooks() {
    try {
      // Parse the JSON string
      final data = json.decode(booksJsonString);

      // Convert to Book objects
      final List<Book> loadedBooks = (data['books'] as List)
          .map((bookJson) => Book.fromJson(bookJson))
          .toList();

      setState(() {
        books = loadedBooks;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        books = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading books: $e');
    }
  }

  void _showCreateObjectPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return CreateObjectPopup(
          onAIGeneratedTap: () {
            Navigator.of(context).pop();
            // Navigate to AI Generated screen
            Provider.of<WebHomeProvider>(context, listen: false)
                .currentScreenIndex = ScreenConstants.aiGeneratedObject;
          },
          onManualCreationTap: () {
            Navigator.of(context).pop();
            // Handle manual creation - you can add your logic here
            // For now, just show a placeholder
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Manual Creation selected')),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff6f6f6),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top navigation bar
          Padding(
            padding:
                const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 316,
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _HoverNavItem(
                                  iconPath: 'assets/images/books-icon.svg',
                                  label: AppLocalizations.of(context)
                                      .translate('library.books'),
                                  isActive:
                                      Provider.of<WebHomeProvider>(context)
                                              .currentScreenIndex ==
                                          ScreenConstants.webMyLibrary,
                                  onTap: () {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants.webMyLibrary;
                                  },
                                ),
                                _HoverNavItem(
                                  iconPath:
                                      'assets/images/square-box-uncheck.svg',
                                  label: AppLocalizations.of(context)
                                      .translate('library.solutions'),
                                  isActive:
                                      Provider.of<WebHomeProvider>(context)
                                              .currentScreenIndex ==
                                          ScreenConstants.webMySolution,
                                  onTap: () {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants.webMySolution;
                                  },
                                ),
                                _HoverNavItem(
                                  iconPath: 'assets/images/cube-box.svg',
                                  label: AppLocalizations.of(context)
                                      .translate('library.objects'),
                                  isActive:
                                      Provider.of<WebHomeProvider>(context)
                                              .currentScreenIndex ==
                                          ScreenConstants.webMyObject,
                                  onTap: () {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants.webMyObject;
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // My Books text
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)
                            .translate('webobject.pageTitle'),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          fontFamily: "TiemposText",
                        ),
                      ),
                    ),
                    // SizedBox(width: 2),

                    // Search bar with filter
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Combined search bar with integrated filter icon
                          Container(
                            width: MediaQuery.of(context).size.width / 3.722,
                            height: 36,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(color: Colors.grey.shade200),
                            ),
                            child: Row(
                              children: [
                                // Search text field
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 16.0),
                                    child: TextField(
                                      decoration: InputDecoration(
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                        hintText: 'Search',
                                        border: InputBorder.none,
                                        hintStyle: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey[500]),
                                        isDense: true,
                                        contentPadding: EdgeInsets.zero,
                                      ),
                                    ),
                                  ),
                                ),

                                // Search icon
                                _HoverSvgButton(
                                  normalIconPath: 'assets/images/search.svg',
                                  hoverIconPath: 'assets/images/search.svg',
                                  onPressed: () {},
                                  imageWidth: 20,
                                  imageHeight: 20,
                                  showBorderOnHover: false,
                                ),

                                // Divider between search and filter
                                Container(
                                  height: 24,
                                  width: 1,
                                  color: Colors.grey.shade200,
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 4),
                                ),

                                // Filter icon - keeping original properties
                                _HoverSvgButton(
                                  normalIconPath:
                                      'assets/images/filter-icon.svg',
                                  hoverIconPath:
                                      'assets/images/filter-hover.svg',
                                  onPressed: () {},
                                  imageWidth: 32,
                                  imageHeight: 32,
                                  showBorderOnHover: false,
                                ),
                                const SizedBox(width: 8),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Create book button
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          HoverCreateButton(
                            text: AppLocalizations.of(context)
                                .translate('webobject.createButtonText'),
                            onPressed: () {
                              _showCreateObjectPopup(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
              ],
            ),
          ),
          // Main content
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.only(
                    left: 94, right: 94, bottom: 0.0, top: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),

                    // Book grid
                    isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : books.isEmpty
                            ? const Center(child: Text('No books found'))
                            : Center(
                                child: LayoutBuilder(
                                  builder: (context, constraints) {
                                    // Determine number of books per row based on screen width
                                    int booksPerRow = MediaQuery.of(context)
                                                .size
                                                .width >=
                                            1920
                                        ? 8
                                        : MediaQuery.of(context).size.width >=
                                                1366
                                            ? 6
                                            : MediaQuery.of(context)
                                                        .size
                                                        .width >=
                                                    1240
                                                ? 6
                                                : MediaQuery.of(context)
                                                            .size
                                                            .width >=
                                                        1024
                                                    ? 3
                                                    : MediaQuery.of(context)
                                                                .size
                                                                .width >=
                                                            768
                                                        ? 2
                                                        : 3;

                                    // Calculate rows needed
                                    int rowCount =
                                        (books.length / booksPerRow).ceil();

                                    // Calculate the width available for each book
                                    double availableWidth =
                                        constraints.maxWidth;
                                    double bookWidth =
                                        136.0; // Default book width
                                    double spacing = (availableWidth -
                                            (bookWidth * booksPerRow)) /
                                        (booksPerRow - 1);

                                    // Ensure spacing is not negative
                                    spacing = spacing < 0 ? 16.0 : spacing;

                                    return Column(
                                      children:
                                          List.generate(rowCount, (rowIndex) {
                                        // Calculate start and end indices for this row
                                        int startIndex = rowIndex * booksPerRow;
                                        int endIndex =
                                            (startIndex + booksPerRow <=
                                                    books.length)
                                                ? startIndex + booksPerRow
                                                : books.length;

                                        // Create a list of books for this row
                                        List<Book> rowBooks =
                                            books.sublist(startIndex, endIndex);

                                        // For last row with fewer items, calculate positions
                                        bool isLastRowWithFewerItems =
                                            rowBooks.length < booksPerRow &&
                                                rowIndex == rowCount - 1;

                                        return Padding(
                                          padding: const EdgeInsets.only(
                                              bottom: 42.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                isLastRowWithFewerItems
                                                    ? MainAxisAlignment.start
                                                    : MainAxisAlignment
                                                        .spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: rowBooks
                                                .asMap()
                                                .entries
                                                .map((entry) {
                                              int idx = entry.key;
                                              Book book = entry.value;

                                              return Padding(
                                                padding:
                                                    isLastRowWithFewerItems &&
                                                            idx <
                                                                rowBooks.length -
                                                                    1
                                                        ? EdgeInsets.only(
                                                            right: spacing)
                                                        : EdgeInsets.zero,
                                                child: _HoverBookCard(
                                                  onTap: () {
                                                    Provider.of<WebHomeProvider>(
                                                                context,
                                                                listen: false)
                                                            .currentScreenIndex =
                                                        ScreenConstants
                                                            .webBookSolution;
                                                  },
                                                  child: _buildBookCard(
                                                    title: book.title,
                                                    versionNumber:
                                                        book.versionNumber,
                                                    subtitle: book.subtitle,
                                                    imageUrl: book.imageUrl,
                                                    isDraft: book.isDraft,
                                                    imageWidth: book.imageWidth,
                                                    imageHeight:
                                                        book.imageHeight,
                                                    index: books.indexOf(book),
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                        );
                                      }),
                                    );
                                  },
                                ),
                              ),
                    Container(
                      // padding: const EdgeInsets.only(right: 45),
                      margin: const EdgeInsets.only(bottom: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // Previous button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_left, size: 20),
                            onPressed: () {
                              // Handle previous page
                            },
                          ),
                          const SizedBox(width: 8),
                          // Next button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_right, size: 20),
                            onPressed: () {
                              // Handle next page
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          //pagination
        ],
      ),
    );
  }

  Widget _buildBookCard({
    required String title,
    String subtitle = '',
    required String versionNumber,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 136.0,
    double imageHeight = 200.0,
    int index = 0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Book cover
        Stack(
          children: [
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                // color: Colors.grey[300],
                // borderRadius: BorderRadius.only(
                //     topRight: Radius.circular(24),
                //     topLeft: Radius.circular(12),
                //     bottomLeft: Radius.circular(12),
                //     bottomRight: Radius.circular(12)),
                // boxShadow: [
                //   BoxShadow(
                //     color: Colors.black.withAlpha(
                //         26), // 0.1 opacity = 26 alpha (255 * 0.1)
                //     blurRadius: 4,
                //     offset: const Offset(0, 2),
                //   ),
                // ],
                image: DecorationImage(
                  image: NetworkImage(imageUrl),
                  // fit: BoxFit.cover,
                  // width: imageWidth,
                  // height: imageHeight,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: 24,
                right: 14,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: imageWidth, // Set the same width as the image
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        // Version Number
        Padding(
          padding: const EdgeInsets.only(top: 10),
          child: Text(
            versionNumber,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 10,
              fontFamily: "TiemposText",
              color: Color(0xff222222),
            ),
          ),
        ),
      ],
    );
  }
}

class _HoverIconButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _HoverIconButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverIconButton> createState() => _HoverIconButtonState();
}

class _HoverIconButtonState extends State<_HoverIconButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isHovered ? Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _HoverPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isHovered ? Color(0xff0058FF) : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isHovered ? Color(0xff0058FF) : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;

  const _HoverNavItem({
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<_HoverNavItem> createState() => _HoverNavItemState();
}

class _HoverNavItemState extends State<_HoverNavItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isHovered ? Colors.white : Colors.transparent,
            border: Border.all(
              color: isHovered ? Color(0xff0058FF) : Colors.transparent,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.iconPath,
                width: 12,
                height: 12,
                colorFilter: ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                widget.label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                  fontWeight:
                      widget.isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _HoverBookCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _HoverBookCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_HoverBookCard> createState() => _HoverBookCardState();
}

class _HoverBookCardState extends State<_HoverBookCard> {
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: widget.child,
      ),
    );
  }
}
